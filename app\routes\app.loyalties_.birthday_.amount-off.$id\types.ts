export interface VIPTier {
  id: number;
  name: string;
  spendRequirement: number | null;
  pointsRequirement: number | null;
}

export interface TierDiscount {
  tierId: number;
  tierName: string;
  rewardValue: number;
  discountType: "percentage" | "fixed";
  minimumRequirement: "none" | "amount" | "quantity";
  minimumValue: number | null;
  hasDiscount: boolean;
  shopifyDiscountId?: string;
  // ✅ NEW: Add combinations for each tier
  combinations?: {
    product: boolean;
    order: boolean;
    shipping: boolean;
  };
}

export interface LoaderData {
  id?: number;
  title: string;
  rewardValue: number;
  discountType: string;
  minimumRequirement: string;
  minimumValue: number | null;
  expiryMonths: number | null;
  productDiscounts?: boolean;
  orderDiscounts?: boolean;
  shippingDiscounts?: boolean;
  shopifyDiscountId?: string;
  hasDifferentTiers: boolean;
  tierDiscounts: TierDiscount[];
  vipTiers: VIPTier[];
}

export interface ActionResponse {
  error?: string;
  success?: boolean;
  message?: string;
  id?: number;
  discounts?: any[];
  count?: number;
}
