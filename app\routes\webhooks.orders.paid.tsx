import type { ActionFunctionArgs } from "@remix-run/node";
import { GET_CUSTOMER_POINTS } from "../graphql/memberQueries";
import { authenticate } from "../shopify.server";
import { MemberMetafield } from "../types/memberTypes";
import {
  getPointOrderSetting,
  getPointsAccoringVipSettings,
  getPointsSettings,
  saveDelayedPoints,
} from "./app.loyalties.points/services";
import { AMOUNT_ENTRY_METHOD, POINT_ENTRY_METHOD } from "./app.loyalties.vip/constants";
import { getVipSettings } from "./app.loyalties.vip/services";
import {
  getNamespaceMetafield,
  updateCustomerPointMetafield,
  updateCustomerTierMetafield,
  updateOrderPointMetafield,
} from "./webhooks.returns.close/services";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { payload, topic, shop, admin } = await authenticate.webhook(request);

  // Get the settings for the shop
  if (!admin) {
    return new Response("Admin not found", { status: 404 });
  }

  // Fetch VIP tiers if the program exists
  const settings = await getVipSettings(shop);
  const vipTiers = settings?.vipTiers;

  const pointSettings = await getPointsSettings(shop);
  const pointsSettings = pointSettings?.pointsSettings;

  const vipTierSetting = await getPointsAccoringVipSettings(shop);
  const vipTiersSettings = vipTierSetting?.vipTiersSettings;

  const pointOrderSettings = await getPointOrderSetting(shop);
  const issueType = pointOrderSettings?.loyaltyPoints?.pointsIssueType || "IMMEDIATE";
  const issueDays = pointOrderSettings?.loyaltyPoints?.issueDays || 0;

  if (admin) {
    const response = await admin.graphql(
      `query getCustomer($id: ID!) {
            customer(id: $id) {
                id
                metafields(first: 10) {
                    edges {
                        node {
                        namespace
                        key
                        value
                        }
                    }
                }
                amountSpent {amount currencyCode}
            }
        }`,
      { variables: { id: payload.customer.admin_graphql_api_id } },
    );
    const responseJson = await response.json();
    const customer = responseJson?.data?.customer;

    // Extract metafields
    const metafields = customer.metafields?.edges.map((m: any) => ({
      namespace: m.node.namespace,
      key: m.node.key,
      value: m.node.value,
    })) as MemberMetafield[];
    const namespace = (await getNamespaceMetafield(admin)) as string;

    // Find VIP tier metafield
    const vipTierMetafield = metafields.find(
      (m) => m.namespace === namespace && m.key === "vip_tier",
    );
    const vipTier = vipTierMetafield ? vipTierMetafield.value : null;

    // Check Order Status for upadte Points
    const orderStatus =
      pointsSettings && pointsSettings[0] ? pointsSettings[0].orderStatus : "PAID_FULFILLED";
    // ────────────────────────────────────────────────────────
    // Compute how many points this order just earned
    // ────────────────────────────────────────────────────────
    let currentAmount = 1;
    let pointsPerCurrency = 1;
    if (vipTiersSettings && vipTiersSettings[0].basedOnDiffTier) {
      // If the points are based on the VIP tier, we need to find the tier of the customer
      const currentTier = vipTiersSettings.find((tier) => tier.name === vipTier);
      currentAmount = currentTier ? (currentTier.spendAmount ?? 1) : 1;
      pointsPerCurrency = currentTier ? (currentTier.pointEarn ?? 1) : 1;
    } else {
      currentAmount = pointsSettings && pointsSettings[0] ? pointsSettings[0].currencyAmount : 1;
      pointsPerCurrency =
        pointsSettings && pointsSettings[0] ? pointsSettings[0].pointsPerCurrency : 1;
    }
    const orderAmount = (Number(payload?.total_price) / currentAmount) * pointsPerCurrency;
    const earnedPoints = Math.floor(orderAmount);

    // ────────────────────────────────────────────────────────
    // Read the customer’s existing points via GraphQL
    // ────────────────────────────────────────────────────────
    const customerGid = payload.customer.admin_graphql_api_id;
    const getRes = await admin.graphql(GET_CUSTOMER_POINTS, {
      variables: { customerId: customerGid },
    });
    const getJson = await getRes.json();
    const existingMf = getJson.data.customer.pointsMf;
    const currentPoints = existingMf?.value ? Number(existingMf.value) : 0;
    let newPoint = currentPoints;

    if (orderStatus !== "PAID_FULFILLED") {
      if (issueType === "IMMEDIATE") {
        // Calculator Point immediate
        newPoint += earnedPoints;
      } else if (issueType === "DELAYED") {
        // Save for Cron Task
        try {
          const issueAt = new Date(Date.now() + issueDays * 24 * 60 * 60 * 1000);
          await saveDelayedPoints({
            customerGid,
            orderId: payload.id.toString(),
            points: earnedPoints,
            issueAt,
          });
        } catch (error) {
          console.error("Error saving delayed points:", error);
        }
      }
    }

    //Update customer & order points & delete redeem discount
    const redeemDiscount = payload?.discount_applications?.find(function (discount: any) {
      return discount?.title?.includes("OMO_POINTS_");
    });

    if (redeemDiscount && payload?.admin_graphql_api_id) {
      const match = redeemDiscount?.title?.match(/OMO_POINTS_(\d+)/);
      const redeemPoint = match ? parseInt(match[1], 10) : 0;
      newPoint = newPoint - redeemPoint;

      if (redeemPoint > 0) {
        await updateOrderPointMetafield(admin, payload.admin_graphql_api_id, String(redeemPoint));
      }
    }

    if (newPoint > 0) {
      await updateCustomerPointMetafield(admin, customer.id, String(newPoint));
    }

    let newTier = null;
    if (vipTiers && vipTiers.length > 0) {
      for (let i = 0; i < vipTiers.length; i++) {
        const spendRequirement = vipTiers[i]?.spendRequirement ?? null;
        const pointsRequirement = vipTiers[i]?.pointsRequirement ?? null;

        if (
          settings?.entryMethod === AMOUNT_ENTRY_METHOD &&
          spendRequirement !== null &&
          customer?.amountSpent?.amount > spendRequirement
        ) {
          newTier = vipTiers[i].name;
        } else if (
          settings?.entryMethod === POINT_ENTRY_METHOD &&
          pointsRequirement !== null &&
          Number(currentPoints) > pointsRequirement
        ) {
          newTier = vipTiers[i].name;
        }
      }
    }

    //update Customer Tier
    if (newTier && vipTier !== newTier) {
      await updateCustomerTierMetafield(admin, customer.id, newTier);
    }
  }
  return new Response();
};
